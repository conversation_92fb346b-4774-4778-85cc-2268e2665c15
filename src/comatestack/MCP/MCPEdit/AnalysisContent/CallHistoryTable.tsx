import {Table, Tag} from 'antd';
import {ColumnsType} from 'antd/es/table';
import {Button} from '@panda-design/components';
import {MCPToolLogItem} from '@/types/mcp/mcp';
import {formatISOTime} from '@/utils/date';

interface Props {
    dataSource: MCPToolLogItem[];
    loading: boolean;
    pagination: {
        current: number;
        pageSize: number;
        total: number;
    };
    sortOrder: {
        createTimeOrder?: 'DESC' | 'ASC';
        durationTimeOrder?: 'DESC' | 'ASC';
        idOrder?: 'DESC' | 'ASC';
    };
    onTableChange: (pagination: any, filters: any, sorter: any) => void;
    onPaginationChange: (page: number, pageSize: number) => void;
    onViewDetail: (record: MCPToolLogItem) => void;
}

const CallHistoryTable = ({
    dataSource,
    loading,
    pagination,
    sortOrder,
    onTableChange,
    onPaginationChange,
    onViewDetail,
}: Props) => {
    const columns: ColumnsType<MCPToolLogItem> = [
        {
            title: '调用ID',
            dataIndex: 'id',
            key: 'id',
            sorter: true,
            sortOrder: sortOrder.idOrder === 'DESC'
                ? 'descend'
                : sortOrder.idOrder === 'ASC'
                    ? 'ascend'
                    : null,
        },
        {
            title: '时间',
            dataIndex: 'createTime',
            key: 'createTime',
            sorter: true,
            sortOrder: sortOrder.createTimeOrder === 'DESC'
                ? 'descend'
                : sortOrder.createTimeOrder === 'ASC'
                    ? 'ascend'
                    : null,
            render: (time: string) => formatISOTime(time),
        },
        {
            title: '耗时',
            dataIndex: 'duration',
            key: 'duration',
            sorter: true,
            sortOrder: sortOrder.durationTimeOrder === 'DESC'
                ? 'descend'
                : sortOrder.durationTimeOrder === 'ASC'
                    ? 'ascend'
                    : null,
            render: (duration: number) => `${duration ? duration : '- '}ms`,
        },
        {
            title: '工具',
            dataIndex: 'toolName',
            key: 'toolName',
        },
        {
            title: '状态',
            dataIndex: 'success',
            key: 'success',
            render: (success: boolean) => (
                <Tag color={success ? 'success' : 'error'}>
                    {success ? '成功' : '失败'}
                </Tag>
            ),
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Button type="link" size="small" onClick={() => onViewDetail(record)}>
                    查看详情
                </Button>
            ),
        },
    ];

    return (
        <Table
            columns={columns}
            dataSource={dataSource}
            rowKey="id"
            loading={loading}
            pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                onChange: onPaginationChange,
                showSizeChanger: true,
                showQuickJumper: true,
            }}
            onChange={onTableChange}
        />
    );
};

export default CallHistoryTable;
