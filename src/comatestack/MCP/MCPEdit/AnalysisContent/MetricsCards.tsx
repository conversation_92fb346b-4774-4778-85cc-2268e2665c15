import {Flex} from 'antd';
import styled from '@emotion/styled';
import {MCPServerAnalysisData} from '@/types/mcp/mcp';

const MetricsCardContainer = styled(Flex)`
    gap: 12px;
`;

const MetricCard = styled.div`
    flex: 1;
    border-radius: 8px;
    padding: 16px;
    background: linear-gradient(180deg, #FBFEFF 0%, #FFFFFF 100%);
    border: 1px solid var(--Tokens-, #E8E8E8)
`;

const MetricLabel = styled.div`
    color: #545454;
    font-size: 14px;
    margin-bottom: 8px;
`;

const MetricValue = styled.div`
    font-size: 24px;
    font-weight: bold;
    color: #000;
`;

interface Props {
    data: MCPServerAnalysisData | null;
}

const MetricsCards = ({data}: Props) => {
    return (
        <MetricsCardContainer>
            <MetricCard>
                <MetricLabel>数据浏览量</MetricLabel>
                <MetricValue>{data?.pv || 0}/{data?.uv || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>总调用量</MetricLabel>
                <MetricValue>{data?.callCount || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>订阅应用量</MetricLabel>
                <MetricValue>{data?.subCount || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>Server Config复制量</MetricLabel>
                <MetricValue>{data?.copyCount || 0}</MetricValue>
            </MetricCard>
            <MetricCard>
                <MetricLabel>总调用成功率</MetricLabel>
                <MetricValue>{data?.callSuccessRate || 0}%</MetricValue>
            </MetricCard>
        </MetricsCardContainer>
    );
};

export default MetricsCards;
